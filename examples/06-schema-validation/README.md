# Schema Validation Example

This example demonstrates how to configure Kong Event Gateway to perform schema validation on Kafka messages.

## Overview

The setup provides:
- Message schema validation
- Integration with Schema Registry
- Validation before message production
- Error handling for invalid messages

## Components

- Apache Kafka broker (localhost:9092)
- Schema Registry (localhost:8081)
- Kong Event Gateway proxy (localhost:19092)

## Quick Start

1. Start the services:
```bash
docker-compose up -d
```

2. Verify the services are running:
```bash
docker ps
```

You should see three containers running:
- `kafka`: The Apache Kafka broker
- `schema-registry`: The Schema Registry service
- `kiburi`: The Kong Event Gateway proxy

## Configuration Details

The `config.yaml` file contains the configuration for schema validation:

```yaml
virtual_clusters:
  - name: proxy
    backend_cluster_name: kafka-localhost
    route_by:
      type: port
      port:
        listen_start: 19092
        min_broker_id: 1
    authentication:
      - type: anonymous
        mediation:
          type: anonymous
```

Key configuration points:
- Schema Registry integration
- Validation rules configuration
- Error handling settings

## Testing

Using kafkactl, test the schema validation:

1. Register a schema:
```bash
curl -X POST -H "Content-Type: application/json" \
    --data '{"schema": "{\"type\":\"record\",\"name\":\"test\",\"fields\":[{\"name\":\"id\",\"type\":\"string\"}]}"}' \
    http://localhost:8081/subjects/test-topic-value/versions
```

2. Produce valid and invalid messages:
```bash
# Valid message
echo '{"id":"123"}' | kafkactl -c virtual produce test-topic

# Invalid message (should fail)
echo '{"wrong":"data"}' | kafkactl -c virtual produce test-topic
```

## Directory Structure

```
05-schema-validation/
├── config.yaml           # Gateway configuration
├── docker-compose.yaml   # Service definitions
└── README.md            # This file
```

## Environment Variables

Required environment variables:
- `KONNECT_CP_HOST`: Konnect Control Plane host
- `KONNECT_PAT`: Personal Access Token

## Use Cases

This schema validation setup is ideal for:
- Ensuring data quality
- Contract-first development
- Multi-team data collaboration
- Data governance implementation

## Troubleshooting

Common issues:

1. Schema validation failures:
   - Verify schema is properly registered
   - Check message format against schema
   - Ensure Schema Registry is accessible

2. Schema Registry connection issues:
   - Verify Schema Registry is running
   - Check network connectivity
   - Confirm registry URL configuration

## Limitations

- Single schema per topic
- No dynamic schema updates
- Basic validation rules only
- Schema evolution constraints

## Next Steps

Explore other examples:
- Basic proxy (01-basic-proxy)
- Topic rewriting (02-topic-rewrite)
- Authentication (03-auth-mediation)
- Message encryption (04-encryption)

## Related Documentation

- [Kong Event Gateway Documentation](https://docs.konghq.com/gateway/)
- [Schema Registry Documentation](https://docs.confluent.io/platform/current/schema-registry/index.html)
- [Apache Avro Documentation](https://avro.apache.org/docs/)