version: '3'
services:
  kafka:
    image: apache/kafka:3.9.0
    container_name: kafka
    network_mode: host
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics.sh --bootstrap-server localhost:9092 --list" ]
      interval: 10s
      timeout: 10s
      retries: 5

  knep:
    image: kong/kong-native-event-proxy-dev:main
    container_name: knep
    depends_on:
      - kafka
    network_mode: host
    environment:
      # KONNECT_CP_HOST: my-konnect-host
      # KONNECT_PAT: my-pat-token
      # Running locally, if possible show running from Konnect!!!!
      KNEP__LOCAL_CONFIG_FILE: /var/config/kiburi/config.yaml
      KNEP__OBSERVABILITY__LOG_FLAGS: debug,knep=debug
    volumes:
      - ./config.yaml:/var/config/kiburi/config.yaml

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak
    network_mode: host    
    environment:
      - KC_BOOTSTRAP_ADMIN_USERNAME=admin
      - KC_BOOTSTRAP_ADMIN_PASSWORD=admin
    command: start-dev --import-realm
    volumes:
      - ./realm-export.json:/opt/keycloak/data/import/realm-export.json
