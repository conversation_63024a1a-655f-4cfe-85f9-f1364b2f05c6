virtual_clusters:
  - name: proxy
    backend_cluster_name: kafka-localhost
    route_by:
      type: port
      port:
        listen_start: 19092
        min_broker_id: 1
    authentication:
      - type: anonymous
        mediation:
          type: anonymous
    topic_rewrite:
      type: cel
      cel:
        virtual_to_backend_expression: >
          {
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>"
          }.has(topic.name) ? 
          {
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>"
          }[topic.name] : topic.name
        backend_to_virtual_expression: >
          {
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>"
          }.has(topic.name) ? 
          {
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>",
            "<PERSON>":"<PERSON>"
          }[topic.name] : topic.name
backend_clusters:
  - name: kafka-localhost
    bootstrap_servers:
      - localhost:9092