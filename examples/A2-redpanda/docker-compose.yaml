name: redpanda-quickstart-one-broker
services:
  redpanda-0:
    image: docker.redpanda.com/redpandadata/redpanda:v22.2.13
    container_name: redpanda-0
    network_mode: host
    command:
      - redpanda
      - start
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      # Address the broker advertises to clients that connect to the Kafka API.
      # Use the internal addresses to connect to the Redpanda brokers'
      # from inside the same Docker network.
      # Use the external addresses to connect to the Redpanda brokers'
      # from outside the Docker network.
      - --advertise-kafka-addr internal://localhost:9092,external://localhost:19092
      - --pandaproxy-addr internal://0.0.0.0:8082,external://0.0.0.0:18082
      # Address the broker advertises to clients that connect to the HTTP Proxy.
      - --advertise-pandaproxy-addr internal://localhost:8082,external://localhost:18082
      - --schema-registry-addr internal://0.0.0.0:8081,external://0.0.0.0:18081
      # Redpanda brokers use the RPC API to communicate with each other internally.
      - --rpc-addr localhost:33145
      - --advertise-rpc-addr localhost:33145
      # Mode dev-container uses well-known configuration properties for development in containers.
      - --mode dev-container
      # Tells Seastar (the framework Redpanda uses under the hood) to use 1 core on the system.
      - --smp 1
      - --default-log-level=info
      - --set node_id=1
      - --set node_id_overrides='{node_id:0,value:1}'

  kiburi:
    image: kong/kiburi:0.0.1
    container_name: kiburi
    depends_on:
      - redpanda-0
    network_mode: host
    environment:
      KONNECT_CP_HOST: my-konnect-host
      KONNECT_PAT: my-pat-token
    volumes:
      - ./config.yaml:/var/config/kiburi/config.yaml
    command: local --config /var/config/kiburi/config.yaml
